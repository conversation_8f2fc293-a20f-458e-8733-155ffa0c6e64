"""Pydantic models for the simplified Manual Trading API.

This consolidates request and response models into a single place
so the API code stays compact and readable.
"""
from __future__ import annotations
from typing import List, Optional
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field
from mattlibrary.trading.child_order_type import ChildOrderType


class InitializationStatus(Enum):
    """Enum for client initialization status."""
    INITIALIZED = "initialized"
    ALREADY_INITIALIZED = "already_initialized"
    UNSUCCESSFUL = "unsuccessful"


class ParentOrderRequest(BaseModel):
    """Request model for submitting parent orders."""
    strategy_id: str
    symbol_id: str
    size: int
    order_type: ChildOrderType
    limit_price: Optional[float]
    stop_price: Optional[float]


class MarketDataRefreshRequest(BaseModel):
    """Request model for market data refresh."""
    symbols: Optional[list[str]]


class SymbolResponse(BaseModel):
    """Response model for symbol data."""
    symbolId: str
    underlying_symbol: str
    assetClass: str
    baseCurrency: str
    exchange: str
    tradingClass: str
    localSymbol: str
    lastTradeDateOrContractMonth: str
    putCall: str
    strike: float
    multiplier: int


class SymbolsResponse(BaseModel):
    """Response model for collection of symbols."""
    symbols: List[SymbolResponse]


class PositionResponse(BaseModel):
    """Response model for position data."""
    position_id: str
    strategy_id: str
    symbol_id: str
    timestamp_current: datetime
    timestamp_first_fill: datetime
    size: int
    average_price: float
    current_price: float
    unrealized_pnl_local: float
    unrealized_pnl_base: float
    realized_pnl_local: float
    realized_pnl_base: float


class PositionsResponse(BaseModel):
    """Response model for collection of positions."""
    positions: List[PositionResponse]


class OrderSubmissionResponse(BaseModel):
    """Response model for order submission."""
    success: bool
    message: str
    order_id: Optional[str]


class PositionsRequestResponse(BaseModel):
    """Response model for positions request."""
    success: bool
    message: str
    positions: List[PositionResponse]


class MarketDataRefreshResponse(BaseModel):
    """Response model for market data refresh."""
    success: bool
    message: str
    positions: List[PositionResponse]


class ClientInitResponse(BaseModel):
    """Response model for client initialization."""
    status: InitializationStatus
    message: str
    client_id: str


class ClientRemoveResponse(BaseModel):
    """Response model for client removal."""
    success: bool
    message: str
    client_id: str

