"""Singleton Manager for handling multiple client instances.

The Manager maintains a dictionary of ClientInstance objects and handles:
- Creating new client instances when clients connect
- Managing the lifecycle of client instances  
- Providing access to client instances for business operations
"""
import logging
from typing import Dict, Optional
from app.client_instance import ClientInstance


class Manager:
    """Singleton manager for handling multiple client instances."""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Manager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.client_instances: Dict[str, ClientInstance] = {}
            self.logger = logging.getLogger(__name__)
            Manager._initialized = True
            self.logger.info("Manager singleton initialized")
    
    
    def create_client_instance(self, client_id: str):
        """Create a new client instance or return existing one."""
        
        # Try to Create new client instance
        try:
            if client_id in self.client_instances:
                self.logger.warning(f"Client {client_id} already exists - keep existing client")
                return

            client_instance = ClientInstance(client_id=client_id)
            self.client_instances[client_id] = client_instance
            self.logger.info(f"Successfully created new client instance: {client_id}")
        except Exception as e:
            self.logger.error(f"Error creating client instance {client_id}: {e}")
            raise


    def remove_client_instance(self, client_id: str):
        """Remove and cleanup client instance."""
        if client_id in self.client_instances:
            try:
                self.client_instances[client_id].cleanup()
                del self.client_instances[client_id]
                self.logger.info(f"Gracefully closed existing client and removed client instance: {client_id}")
            except Exception as e:
                self.logger.error(f"Error removing client instance {client_id}: {e}")
        else:
            self.logger.warning(f"Client {client_id} not found - could not remove")
        

    def get_client_instance(self, client_id: str) -> Optional[ClientInstance]:
        """Get existing client instance."""
        return self.client_instances.get(client_id)
    

    def get_all_client_ids(self) -> list[str]:
        """Get all active client IDs."""
        return list(self.client_instances.keys())
    
    
    def cleanup_all_clients(self):
        """Cleanup all client instances."""
        self.logger.info("Cleaning up all client instances...")
        client_ids = list(self.client_instances.keys())
        
        for client_id in client_ids:
            self.remove_client_instance(client_id)
        
        self.logger.info("All client instances cleaned up")


# Global singleton instance
manager = Manager()
