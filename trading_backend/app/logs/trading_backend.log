INFO - 2025-08-18 22:35:19,868 - app.manager - Manager singleton initialized
INFO - 2025-08-18 22:35:19,876 - app.communicator - Communicator singleton initialized
INFO - 2025-08-18 22:35:19,877 - app.communicator - Starting trading backend
INFO - 2025-08-18 22:35:36,384 - app.client_instance - Initializing client instance: client100
INFO - 2025-08-18 22:35:36,384 - mattlibrary.trading.execution_data_engines.simulated_execution_engine - SimulatedExecutionEngine initialized
INFO - 2025-08-18 22:35:36,452 - mattlibrary.trading.fx_converter - Loaded fx conversion data (4553 rows) from trading_backend/app/data/fx_converter_data.parquet
INFO - 2025-08-18 22:35:36,457 - mattlibrary.trading.symbol_database - Loaded 28 symbols from trading_backend/app/data/symbol_database.parquet
INFO - 2025-08-18 22:35:36,457 - mattlibrary.trading.trading_engine - 1 Positions loaded from disk: trading_backend/app/data/positions_client100.json
INFO - 2025-08-18 22:35:36,457 - mattlibrary.trading.trading_engine - Initialized Trading Engine [base_currency=USD, starting_balance=100000 USD, track_performance=True, execution_plugin_type=SimulatedExecutionEngine, static_data_directory=trading_backend/app/data/]
INFO - 2025-08-18 22:35:36,457 - mattlibrary.datamanagement.interactive_brokers - Initializing InteractiveBrokersAPI
INFO - 2025-08-18 22:35:36,458 - app.client_instance - Client instance initialized successfully: client100 with IB client ID 963
INFO - 2025-08-18 22:35:36,458 - app.manager - Successfully created new client instance: client100
INFO - 2025-08-18 22:35:36,461 - app.client_instance - Retrieved 1 positions for client client100
INFO - 2025-08-18 22:35:36,462 - app.communicator - Positions requested for client client100. Total positions: 1
INFO - 2025-08-18 22:35:36,859 - app.communicator - WebSocket connected for client: client100
INFO - 2025-08-18 22:36:08,567 - ib_async.client - Connecting to 127.0.0.1:7497 with clientId 963...
INFO - 2025-08-18 22:36:08,568 - ib_async.client - Connected
INFO - 2025-08-18 22:36:08,573 - ib_async.client - Logged on to server version 178
INFO - 2025-08-18 22:36:08,574 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usfarm.nj
INFO - 2025-08-18 22:36:08,574 - ib_async.client - API connection ready
INFO - 2025-08-18 22:36:08,575 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usfuture
INFO - 2025-08-18 22:36:08,575 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:cashfarm
INFO - 2025-08-18 22:36:08,575 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usfarm
INFO - 2025-08-18 22:36:08,575 - ib_async.wrapper - Warning 2105, reqId -1: HMDS data farm connection is broken:euhmds
INFO - 2025-08-18 22:36:08,576 - ib_async.wrapper - Warning 2105, reqId -1: HMDS data farm connection is broken:cashhmds
INFO - 2025-08-18 22:36:08,576 - ib_async.wrapper - Warning 2105, reqId -1: HMDS data farm connection is broken:fundfarm
INFO - 2025-08-18 22:36:08,576 - ib_async.wrapper - Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
INFO - 2025-08-18 22:36:08,576 - ib_async.wrapper - Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
INFO - 2025-08-18 22:36:08,677 - ib_async.ib - Synchronization complete
INFO - 2025-08-18 22:36:08,677 - mattlibrary.datamanagement.interactive_brokers - Connected to Interactive Brokers
INFO - 2025-08-18 22:36:29,275 - app.client_instance - Cleaning up client instance: client100
INFO - 2025-08-18 22:36:29,276 - ib_async.ib - Disconnecting from 127.0.0.1:7497, 202 B sent in 9 messages, 21.2 kB received in 450 messages, session time 52.8 s.
INFO - 2025-08-18 22:36:29,276 - ib_async.client - Disconnecting
INFO - 2025-08-18 22:36:29,276 - mattlibrary.datamanagement.interactive_brokers - Disconnected from Interactive Brokers
INFO - 2025-08-18 22:36:29,276 - mattlibrary.trading.trading_engine - Trading Engine finalized [strategies=0]
INFO - 2025-08-18 22:36:29,277 - app.client_instance - Client instance cleanup completed: client100
INFO - 2025-08-18 22:36:29,277 - app.manager - Gracefully closed existing client and removed client instance: client100
INFO - 2025-08-18 22:36:29,277 - ib_async.client - Disconnected.
INFO - 2025-08-18 22:36:29,280 - app.communicator - WebSocket client disconnected: client100
INFO - 2025-08-18 22:40:42,606 - app.client_instance - Initializing client instance: client100
INFO - 2025-08-18 22:40:42,607 - mattlibrary.trading.execution_data_engines.simulated_execution_engine - SimulatedExecutionEngine initialized
INFO - 2025-08-18 22:40:42,621 - mattlibrary.trading.fx_converter - Loaded fx conversion data (4553 rows) from trading_backend/app/data/fx_converter_data.parquet
INFO - 2025-08-18 22:40:42,624 - mattlibrary.trading.symbol_database - Loaded 28 symbols from trading_backend/app/data/symbol_database.parquet
INFO - 2025-08-18 22:40:42,625 - mattlibrary.trading.trading_engine - 1 Positions loaded from disk: trading_backend/app/data/positions_client100.json
INFO - 2025-08-18 22:40:42,625 - mattlibrary.trading.trading_engine - Initialized Trading Engine [base_currency=USD, starting_balance=100000 USD, track_performance=True, execution_plugin_type=SimulatedExecutionEngine, static_data_directory=trading_backend/app/data/]
INFO - 2025-08-18 22:40:42,625 - mattlibrary.datamanagement.interactive_brokers - Initializing InteractiveBrokersAPI
INFO - 2025-08-18 22:40:42,626 - app.client_instance - Client instance initialized successfully: client100 with IB client ID 963
INFO - 2025-08-18 22:40:42,626 - app.manager - Successfully created new client instance: client100
INFO - 2025-08-18 22:40:42,630 - app.client_instance - Retrieved 1 positions for client client100
INFO - 2025-08-18 22:40:42,630 - app.communicator - Positions requested for client client100. Total positions: 1
INFO - 2025-08-18 22:40:43,026 - app.communicator - WebSocket connected for client: client100
INFO - 2025-08-18 22:40:49,535 - ib_async.client - Connecting to 127.0.0.1:7497 with clientId 963...
INFO - 2025-08-18 22:40:49,535 - ib_async.client - Connected
INFO - 2025-08-18 22:40:49,541 - ib_async.client - Logged on to server version 178
INFO - 2025-08-18 22:40:49,587 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usfarm.nj
INFO - 2025-08-18 22:40:49,587 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usfuture
INFO - 2025-08-18 22:40:49,587 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:cashfarm
INFO - 2025-08-18 22:40:49,588 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usfarm
INFO - 2025-08-18 22:40:49,588 - ib_async.wrapper - Warning 2105, reqId -1: HMDS data farm connection is broken:euhmds
INFO - 2025-08-18 22:40:49,588 - ib_async.wrapper - Warning 2105, reqId -1: HMDS data farm connection is broken:cashhmds
INFO - 2025-08-18 22:40:49,588 - ib_async.wrapper - Warning 2105, reqId -1: HMDS data farm connection is broken:fundfarm
INFO - 2025-08-18 22:40:49,589 - ib_async.wrapper - Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
INFO - 2025-08-18 22:40:49,589 - ib_async.wrapper - Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
INFO - 2025-08-18 22:40:49,589 - ib_async.client - API connection ready
INFO - 2025-08-18 22:40:49,693 - ib_async.ib - Synchronization complete
INFO - 2025-08-18 22:40:49,694 - mattlibrary.datamanagement.interactive_brokers - Connected to Interactive Brokers
INFO - 2025-08-18 22:41:02,155 - app.communicator - WebSocket client disconnected: client100
WARNING - 2025-08-18 22:41:07,375 - app.manager - Client client100 already exists - keep existing client
INFO - 2025-08-18 22:41:07,378 - app.client_instance - Retrieved 1 positions for client client100
INFO - 2025-08-18 22:41:07,378 - app.communicator - Positions requested for client client100. Total positions: 1
INFO - 2025-08-18 22:41:07,772 - app.communicator - WebSocket connected for client: client100
INFO - 2025-08-18 22:41:15,660 - ib_async.client - Connecting to 127.0.0.1:7497 with clientId 963...
INFO - 2025-08-18 22:41:15,660 - ib_async.client - Disconnected.
ERROR - 2025-08-18 22:41:15,661 - app.client_instance - Error refreshing market data for client client100: Socket disconnect
INFO - 2025-08-18 22:41:15,661 - mattlibrary.datamanagement.interactive_brokers - Disconnected from Interactive Brokers
INFO - 2025-08-18 22:41:15,662 - ib_async.client - Connected
INFO - 2025-08-18 22:41:15,664 - ib_async.client - Logged on to server version 178
INFO - 2025-08-18 22:41:15,707 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usfarm.nj
INFO - 2025-08-18 22:41:15,707 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usfuture
INFO - 2025-08-18 22:41:15,708 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:cashfarm
INFO - 2025-08-18 22:41:15,708 - ib_async.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usfarm
INFO - 2025-08-18 22:41:15,708 - ib_async.wrapper - Warning 2105, reqId -1: HMDS data farm connection is broken:euhmds
INFO - 2025-08-18 22:41:15,709 - ib_async.wrapper - Warning 2105, reqId -1: HMDS data farm connection is broken:cashhmds
INFO - 2025-08-18 22:41:15,709 - ib_async.wrapper - Warning 2105, reqId -1: HMDS data farm connection is broken:fundfarm
INFO - 2025-08-18 22:41:15,710 - ib_async.wrapper - Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
INFO - 2025-08-18 22:41:15,710 - ib_async.wrapper - Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
INFO - 2025-08-18 22:41:15,710 - ib_async.client - API connection ready
INFO - 2025-08-18 22:41:15,822 - ib_async.ib - Synchronization complete
INFO - 2025-08-18 22:41:15,822 - mattlibrary.datamanagement.interactive_brokers - Connected to Interactive Brokers
INFO - 2025-08-18 22:41:22,888 - app.client_instance - Cleaning up client instance: client100
INFO - 2025-08-18 22:41:22,888 - ib_async.ib - Disconnecting from 127.0.0.1:7497, 202 B sent in 9 messages, 21.2 kB received in 450 messages, session time 7.23 s.
INFO - 2025-08-18 22:41:22,888 - ib_async.client - Disconnecting
INFO - 2025-08-18 22:41:22,888 - mattlibrary.datamanagement.interactive_brokers - Disconnected from Interactive Brokers
INFO - 2025-08-18 22:41:22,889 - mattlibrary.trading.trading_engine - Trading Engine finalized [strategies=0]
INFO - 2025-08-18 22:41:22,889 - app.client_instance - Client instance cleanup completed: client100
INFO - 2025-08-18 22:41:22,889 - app.manager - Gracefully closed existing client and removed client instance: client100
INFO - 2025-08-18 22:41:22,889 - ib_async.client - Disconnected.
INFO - 2025-08-18 22:41:27,855 - app.communicator - WebSocket client disconnected: client100
