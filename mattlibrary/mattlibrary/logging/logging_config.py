"""Logging configuration module."""

import logging
import logging.config
import os
import time  # Add this import


class CallbackHandler(logging.Handler):
    """Custom logging handler to send log records to a callback."""
    def __init__(self, callback):
        super().__init__()
        self.callback = callback
        # Set the converter attribute (required by logging handlers)
        self.converter = time.gmtime

    def emit(self, record):
        self.callback(record)

    def formatTime(self, record, datefmt=None):
        ct = self.converter(record.created)
        if datefmt:
            s = time.strftime(datefmt, ct)
        else:
            s = time.strftime("%Y-%m-%d %H:%M:%S", ct)
        return s


def setup_logging(
    logging_enabled,
    log_level, 
    log_to_console, 
    log_to_file,
    log_file=None, 
    clean_log_file=False,
    log_callback=None  # <-- Add this argument
):
    """Configure logging for the backtesting system.
    
    Args:
        logging_enabled: If False, no logging will be configured.
        log_level: The logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL).
        log_to_console: Whether to output logs to console.
        log_to_file: Whether to output logs to file.
        log_file: Path to the log file.
        clean_log_file: Whether to clean the log file before writing to it.
        log_callback: Optional callback to receive log messages as dicts.
    """
    
    if not logging_enabled:
        return
        
    config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'detailed': {
                'format': '%(levelname)s - %(asctime)s - %(name)s - %(message)s'
            },
            'simple': {
                'format': '%(levelname)s - %(message)s'
            }
        },
        'handlers': {},
        'loggers': {
            '': {  # Root logger
                'handlers': [],
                'level': log_level,
                'propagate': True
            }
        }
    }
    
    # Configure console output
    if log_to_console:
        config['handlers']['console'] = {
            'class': 'logging.StreamHandler',
            'level': log_level,
            'formatter': 'detailed',
        }
        config['loggers']['']['handlers'].append('console')
    
    # Configure file output
    if log_to_file:
        # Clean log file if requested
        if clean_log_file and os.path.exists(log_file):
            try:
                os.remove(log_file)
            except Exception as e:
                print(f"Failed to clean log file: {e}")
        
        # Ensure the directory exists
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        config['handlers']['file'] = {
            'class': 'logging.FileHandler',
            'filename': log_file,
            'mode': 'w' if clean_log_file else 'a',
            'level': log_level,
            'formatter': 'detailed',
        }
        config['loggers']['']['handlers'].append('file')
    
    # Validate that at least one handler is configured
    if not config['loggers']['']['handlers'] and not log_callback:
        raise ValueError("At least one of log_to_console, log_to_file, or log_callback must be enabled")
    
    # Apply the logging configuration first
    logging.config.dictConfig(config)
    
    # Add callback handler AFTER dictConfig to prevent it from being overwritten
    if log_callback:
        callback_handler = CallbackHandler(log_callback)
        callback_handler.setLevel(getattr(logging, log_level.upper()))
        logging.getLogger().addHandler(callback_handler)
    