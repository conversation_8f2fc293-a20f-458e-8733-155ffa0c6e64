<script lang="ts">
  import { page } from '$app/stores';
  import '../app.css';
  import { onMount } from 'svelte';
  import { logStore } from '$lib/stores/logs.svelte';
  import { initializeClientStore } from '$lib/stores/client.svelte';
  
  // Import page components
  import DashboardPage from '$lib/components/DashboardPage.svelte';
  import TradingPage from '$lib/components/TradingPage.svelte';

  const navLinks = [
    { href: '/dashboard', label: 'Dashboard' },
    { href: '/trading', label: 'Simulated Trading' },
  ];

  onMount(() => {
    // Initialize client store but don't auto-connect
    initializeClientStore();
    
    // Don't auto-load positions - wait for user to connect
  });
</script>

<div class="app-container">
  <nav class="sidebar">
    <div class="sidebar-header">
      <div class="brand">Trading Frontend</div>
    </div>
    <ul class="nav-links">
      {#each navLinks as link}
        <li>
          <a 
            href={link.href} 
            class="nav-link"
            aria-current={$page.url.pathname.startsWith(link.href) ? 'page' : undefined}
          >
            {link.label}
          </a>
        </li>
      {/each}
    </ul>
  </nav>

  <main class="content">
    <!-- SPA-style show/hide all pages -->
    <div class:hidden={$page.url.pathname !== '/dashboard'}>
      <DashboardPage />
    </div>
    <div class:hidden={!$page.url.pathname.startsWith('/trading')}>
      <TradingPage />
    </div>
    
    <!-- Slot for additional content if needed -->
    <slot />
  </main>
</div>

<style>
  .hidden {
    display: none;
  }

  .app-container {
    display: flex;
    min-height: 100vh;
    background: var(--background);
  }

  .sidebar {
    width: 280px;
    background: var(--surface);
    border-right: 1px solid var(--border);
    display: flex;
    flex-direction: column;
  }

  .sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
  }

  .brand {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--accent);
  }

  .nav-links {
    list-style: none;
    padding: 1rem 0;
    margin: 0;
    flex: 1;
  }

  .nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1.5rem;
    color: var(--text);
    text-decoration: none;
    transition: all 0.2s;
    font-weight: 500;
  }

  .nav-link:hover {
    background: var(--hover);
    color: var(--accent);
  }

  .nav-link[aria-current="page"] {
    background: var(--accent-light);
    color: var(--accent);
    border-right: 3px solid var(--accent);
  }

  .content {
    flex: 1;
    overflow-y: auto;
  }

  @media (max-width: 768px) {
    .app-container {
      flex-direction: column;
    }

    .sidebar {
      width: 100%;
      height: auto;
    }

    .nav-links {
      display: flex;
      overflow-x: auto;
      padding: 0.5rem;
    }

    .nav-link {
      white-space: nowrap;
      padding: 0.5rem 1rem;
      border-radius: 0.5rem;
      margin-right: 0.5rem;
    }

    .nav-link[aria-current="page"] {
      border-right: none;
      border-bottom: 3px solid var(--accent);
    }
  }
</style>
