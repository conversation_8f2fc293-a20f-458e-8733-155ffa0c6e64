import { browser } from '$app/environment';

// Client connection store using Svelte 5 runes
export let clientStore = $state({
  clientId: '',
  isConnected: false,
  lastContact: null as Date | null,
  connectionStatus: 'disconnected' as 'disconnected' | 'connecting' | 'connected' | 'error',
  error: null as string | null,
});

// Load client_id from localStorage if available
export function initializeClientStore() {
  if (!browser) return;
  
  const savedClientId = localStorage.getItem('clientId');
  if (savedClientId) {
    clientStore.clientId = savedClientId;
  }
}

// Save client_id to localStorage
export function saveClientId(clientId: string) {
  if (!browser) return;
  
  clientStore.clientId = clientId;
  localStorage.setItem('clientId', clientId);
}

// Update connection status
export function updateConnectionStatus(status: typeof clientStore.connectionStatus, error?: string) {
  clientStore.connectionStatus = status;
  clientStore.isConnected = status === 'connected';
  clientStore.error = error || null;
  
  if (status === 'connected') {
    clientStore.lastContact = new Date();
  }
}

// Update last contact time
export function updateLastContact() {
  clientStore.lastContact = new Date();
}
