import { browser } from '$app/environment';
import { clientStore, updateLastContact } from './client.svelte';
import type { LogMessage } from '$lib/types';

// Global store state using Svelte 5 runes
export let logStore = $state({
  logMessages: [] as LogMessage[],
  wsConnected: false,
  wsError: null as string | null,
  sortColumn: null as keyof LogMessage | null,
  sortDirection: 'asc' as 'asc' | 'desc',
  filterTimestamp: '',
  filterLevel: '',
  filterSource: '',
  filterMessage: '',
});

let ws: WebSocket | null = null;
let reconnectTimer: number | null = null;
const RECONNECT_DELAY = 3000; // 3 seconds

// Initialize websocket connection - called when client connects
export function initializeWebSocket(clientId: string) {
  if (!browser) {
    console.log('Not in browser environment, skipping WebSocket connection');
    return;
  }
  
  if (!clientId) {
    console.log('No client_id available, skipping WebSocket connection for logs');
    return;
  }
  
  console.log('Initializing WebSocket with client ID:', clientId);
  connectWebSocket(clientId);
}

function connectWebSocket(clientId: string) {
  if (ws?.readyState === WebSocket.CONNECTING || ws?.readyState === WebSocket.OPEN) {
    return; // Already connected or connecting
  }

  const WS_URL = `ws://localhost:8000/ws/${clientId}`;
  console.log('Attempting to connect WebSocket for logs with client ID:', clientId);
  ws = new WebSocket(WS_URL);

  ws.onopen = () => {
    logStore.wsConnected = true;
    logStore.wsError = null;
    console.log('WebSocket connected for logs.');
    updateLastContact();
    
    // Clear any reconnection timer
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }
  };

  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      console.log('Received WebSocket data:', data);
      
      if (data.type === 'log') {
        console.log('Processing log message:', data);
        const logEntry: LogMessage = {
          timestamp: new Date(data.timestamp * 1000).toISOString(),
          level: data.level || 'UNKNOWN',
          source: data.logger_name || 'UNKNOWN',
          message: data.message || '',
        };
        console.log('Created log entry:', logEntry);
        logStore.logMessages.push(logEntry);
        updateLastContact();
      }
    } catch (e) {
      console.error('Failed to parse WebSocket message:', e);
    }
  };

  ws.onclose = (event) => {
    logStore.wsConnected = false;
    if (event.wasClean) {
      logStore.wsError = 'WebSocket connection closed.';
      console.log('WebSocket closed cleanly for logs.');
    } else {
      logStore.wsError = `WebSocket disconnected: ${event.code} - ${event.reason}`;
      console.log('WebSocket disconnected unexpectedly for logs:', event);
      
      // Attempt to reconnect if we have a client ID
      if (clientStore.clientId) {
        scheduleReconnect(clientStore.clientId);
      }
    }
  };

  ws.onerror = (errorEvent) => {
    logStore.wsConnected = false;
    logStore.wsError = 'WebSocket error occurred.';
    console.error('WebSocket error for logs:', errorEvent);
  };
}

function scheduleReconnect(clientId: string) {
  if (reconnectTimer) return; // Reconnection already scheduled
  
  console.log(`Scheduling WebSocket reconnection in ${RECONNECT_DELAY}ms...`);
  reconnectTimer = setTimeout(() => {
    reconnectTimer = null;
    connectWebSocket(clientId);
  }, RECONNECT_DELAY);
}

// Manual reconnection function
export function reconnectWebSocket() {
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }
  if (clientStore.clientId) {
    connectWebSocket(clientStore.clientId);
  }
}

// Clean shutdown - called when client disconnects
export function disconnectWebSocket() {
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }
  if (ws) {
    ws.close(1000, 'Client disconnect');
    ws = null;
  }
  logStore.wsConnected = false;
}

export function setSort(column: keyof LogMessage) {
  if (logStore.sortColumn === column) {
    logStore.sortDirection = logStore.sortDirection === 'asc' ? 'desc' : 'asc';
  } else {
    logStore.sortColumn = column;
    logStore.sortDirection = 'asc';
  }
}

// Derived state for filtered and sorted logs
const _filteredAndSortedLogs = $derived.by(() => {
  let filtered = logStore.logMessages.filter(log => {
    // Safely handle potentially undefined/null values
    const timestamp = log.timestamp || '';
    const level = log.level || '';
    const source = log.source || '';
    const message = log.message || '';
    
    // Only apply filters if they have values
    const timestampMatch = !logStore.filterTimestamp || timestamp.includes(logStore.filterTimestamp);
    const levelMatch = !logStore.filterLevel || level.toLowerCase().includes(logStore.filterLevel.toLowerCase());
    const sourceMatch = !logStore.filterSource || source.toLowerCase().includes(logStore.filterSource.toLowerCase());
    const messageMatch = !logStore.filterMessage || message.toLowerCase().includes(logStore.filterMessage.toLowerCase());
    
    return timestampMatch && levelMatch && sourceMatch && messageMatch;
  });

  if (logStore.sortColumn) {
    filtered.sort((a, b) => {
      const sortColumn = logStore.sortColumn!;
      const aValue = a[sortColumn];
      const bValue = b[sortColumn];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return logStore.sortDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }
      if (aValue < bValue) return logStore.sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return logStore.sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }
  return filtered;
});

export function getFilteredAndSortedLogs() {
  return _filteredAndSortedLogs;
}
