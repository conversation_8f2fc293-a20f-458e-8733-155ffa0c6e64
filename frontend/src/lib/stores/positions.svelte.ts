import { fetchPositions, refreshMarketData } from '$lib/api';
import { clientStore } from './client.svelte';
import type { Position } from '$lib/types';

// Export a single $state object
export let positionsStore = $state({
  positions: [] as Position[],
  isLoading: false,
  error: null as string | null,
  isInitialized: false, // Internal flag
});

export async function loadPositions(refresh = false) {
  // Don't fetch if no client ID is available
  if (!clientStore.clientId) {
    positionsStore.positions = [];
    positionsStore.error = 'No client ID configured';
    return;
  }

  // Only fetch if it's a manual refresh or if it's the very first time
  if (!refresh && positionsStore.isInitialized) {
    return;
  }

  positionsStore.isLoading = true;
  positionsStore.error = null;
  try {
    const apiCall = refresh ? refreshMarketData : fetchPositions;
    console.log(`${refresh ? 'Refreshing market data' : 'Fetching positions'}...`);
    const response = await apiCall();
    console.log('API response:', response);
    if (response.success) {
      // Force reactivity by creating a new array reference
      positionsStore.positions = [...response.positions];
      positionsStore.isInitialized = true; // Mark as initialized
      console.log('Updated positions:', positionsStore.positions.length, 'positions');
    } else {
      throw new Error(response.message);
    }
  } catch (e) {
    if (e instanceof Error) {
      positionsStore.error = e.message;
    } else {
      positionsStore.error = 'An unknown error occurred.';
    }
  } finally {
    positionsStore.isLoading = false;
  }
}
