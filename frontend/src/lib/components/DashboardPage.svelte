<script lang="ts">
  import { tick } from 'svelte';
  import { onMount } from 'svelte';
  import { clientStore, saveClientId, updateConnectionStatus, initializeClientStore } from '$lib/stores/client.svelte';
  import { initializeWebSocket, disconnectWebSocket, logStore, getFilteredAndSortedLogs, setSort } from '$lib/stores/logs.svelte';
  import { loadPositions } from '$lib/stores/positions.svelte';
  import { initializeClient, recreateClient, removeClient } from '$lib/api';
  import { formatTimestamp } from '$lib/formatters';
  import type { LogMessage } from '$lib/types';

  let isLoading = $state(false);
  let tempClientId = $state('');
  let tableContainer: HTMLDivElement | undefined;
  let lastMessageCount = 0;

  onMount(() => {
    initializeClientStore();
    tempClientId = clientStore.clientId;
    lastMessageCount = logStore.logMessages.length;
  });

  function getSortIndicator(column: keyof LogMessage) {
    if (logStore.sortColumn === column) {
      return logStore.sortDirection === 'asc' ? ' ▲' : ' ▼';
    }
    return '';
  }

  // Auto-scroll to bottom when new messages arrive
  $effect.pre(() => {
    if (!tableContainer) return;

    const currentMessageCount = logStore.logMessages.length;
    
    if (currentMessageCount > lastMessageCount) {
      const isAtBottom = tableContainer.offsetHeight + tableContainer.scrollTop >= tableContainer.scrollHeight - 50;
      if (isAtBottom) {
        tick().then(() => {
          if (tableContainer) {
            tableContainer.scrollTo(0, tableContainer.scrollHeight);
          }
        });
      }
      lastMessageCount = currentMessageCount;
    }
  });

  async function handleConnect() {
    if (!tempClientId.trim()) {
      updateConnectionStatus('error', 'Please enter a client ID');
      return;
    }

    const clientIdToConnect = tempClientId.trim(); // Capture current value
    isLoading = true;
    updateConnectionStatus('connecting');

    try {
      // First establish HTTP connection and verify backend is working
      const response = await initializeClient(clientIdToConnect);
      
      // Only proceed if backend responds with success
      if (response.success) {
        saveClientId(clientIdToConnect);
        updateConnectionStatus('connected');
        
        // Now that HTTP is working, establish WebSocket connection
        disconnectWebSocket();
        setTimeout(() => {
          initializeWebSocket(clientIdToConnect);
        }, 100);
        
        // Load initial positions data
        await loadPositions();
      } else {
        updateConnectionStatus('error', response.message || 'Backend initialization failed');
      }
      
    } catch (error) {
      console.error('Failed to connect:', error);
      updateConnectionStatus('error', error instanceof Error ? error.message : 'Connection failed');
    } finally {
      isLoading = false;
    }
  }

  async function handleReconnect() {
    if (!clientStore.clientId) {
      updateConnectionStatus('error', 'No client ID configured');
      return;
    }

    const currentClientId = clientStore.clientId; // Capture current value
    isLoading = true;
    updateConnectionStatus('connecting');

    try {
      // First recreate HTTP connection and verify backend is working
      const response = await recreateClient(currentClientId);
      
      // Only proceed if backend responds with success
      if (response.success) {
        updateConnectionStatus('connected');
        
        // Now that HTTP is working, re-establish WebSocket connection
        disconnectWebSocket();
        setTimeout(() => {
          initializeWebSocket(currentClientId);
        }, 100);
        
        // Reload positions after reconnection
        await loadPositions();
      } else {
        updateConnectionStatus('error', response.message || 'Backend reconnection failed');
      }
      
    } catch (error) {
      console.error('Failed to reconnect:', error);
      updateConnectionStatus('error', error instanceof Error ? error.message : 'Reconnection failed');
    } finally {
      isLoading = false;
    }
  }

  async function handleDisconnect() {
    if (!clientStore.clientId) return;

    isLoading = true;

    try {
      // First remove client from backend and verify it responds successfully
      const response = await removeClient(clientStore.clientId);
      
      // Only close WebSocket if backend responds with success
      if (response.success) {
        updateConnectionStatus('disconnected');
        
        // Wait 5 seconds to capture all disconnect log messages before closing WebSocket
        setTimeout(() => {
          disconnectWebSocket();
        }, 5000);
      } else {
        updateConnectionStatus('error', response.message || 'Backend disconnect failed');
      }
      
    } catch (error) {
      console.error('Failed to disconnect:', error);
      updateConnectionStatus('error', error instanceof Error ? error.message : 'Disconnect failed');
    } finally {
      isLoading = false;
    }
  }

  function getStatusColor(status: typeof clientStore.connectionStatus) {
    switch (status) {
      case 'connected': return 'var(--positive)';
      case 'connecting': return 'var(--accent)';
      case 'error': return 'var(--negative)';
      default: return 'var(--muted)';
    }
  }

  function getStatusText(status: typeof clientStore.connectionStatus) {
    switch (status) {
      case 'connected': return 'Connected';
      case 'connecting': return 'Connecting...';
      case 'error': return 'Error';
      default: return 'Disconnected';
    }
  }

  function formatDateTimestamp(date: Date | null) {
    if (!date) return 'Never';
    return date.toLocaleString();
  }
</script>

<svelte:head>
  <title>Dashboard</title>
</svelte:head>

<div class="page-container">
  <h1>Dashboard</h1>
  
  <!-- Connection Control Section -->
  <section class="card" style="margin-bottom: 1rem;">
    <h2 style="margin-top: -0.5rem; margin-bottom: 0.75rem;">Connection Settings</h2>
    
    <div class="connection-form">
      <div class="form-group">
        <label for="client-id">Client ID:</label>
        <input 
          id="client-id"
          type="text" 
          bind:value={tempClientId}
          placeholder="Enter client ID (e.g., trading_client_001)"
          disabled={isLoading}
          class="text-input"
        />
      </div>
      
      <div class="button-group">
        <button 
          class="btn btn-primary" 
          onclick={handleConnect}
        >
          {isLoading && clientStore.connectionStatus === 'connecting' ? 'Connecting...' : 'Connect'}
        </button>
        
        <button 
          class="btn btn-secondary" 
          onclick={handleReconnect}
        >
          {isLoading && clientStore.connectionStatus === 'connecting' ? 'Reconnecting...' : 'Reconnect'}
        </button>
        
        <button 
          class="btn btn-danger" 
          onclick={handleDisconnect}
        >
          Disconnect
        </button>
      </div>
    </div>
  </section>

  <!-- Connection Status Section -->
  <section class="card" style="margin-bottom: 1rem;">
    <h2 style="margin-top: -0.5rem; margin-bottom: 0.75rem;">Connection Status</h2>
    
    <div class="status-grid">
      <div class="status-item">
        <span class="status-label">Backend Connection:</span>
        <div class="status-value">
          <span 
            class="status-indicator" 
            style="background-color: {getStatusColor(clientStore.connectionStatus)}"
          ></span>
          <span class="status-text">
            {getStatusText(clientStore.connectionStatus)}
          </span>
        </div>
      </div>

      <div class="status-item">
        <span class="status-label">WebSocket Connection:</span>
        <div class="status-value">
          <span 
            class="status-indicator" 
            style="background-color: {logStore.wsConnected ? 'var(--positive)' : 'var(--negative)'}"
          ></span>
          <span class="status-text">
            {logStore.wsConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
      </div>

      <div class="status-item">
        <span class="status-label">Client ID:</span>
        <div class="status-value">
          <span class="client-id-display">
            {clientStore.clientId || 'Not configured'}
          </span>
        </div>
      </div>

        <div class="status-item">
          <span class="status-label">Last Contact:</span>
          <div class="status-value">
            <span class="timestamp">
              {formatDateTimestamp(clientStore.lastContact)}
            </span>
          </div>
        </div>      {#if clientStore.error}
        <div class="status-item error">
          <span class="status-label">Error:</span>
          <div class="status-value">
            <span class="error-message">
              {clientStore.error}
            </span>
          </div>
        </div>
      {/if}

      {#if logStore.wsError}
        <div class="status-item error">
          <span class="status-label">WebSocket Error:</span>
          <div class="status-value">
            <span class="error-message">
              {logStore.wsError}
            </span>
          </div>
        </div>
      {/if}
    </div>
  </section>

  <!-- Log Messages Section -->
  <section class="card">
    <h2 style="margin-top: -0.5rem; margin-bottom: 0.75rem;">Log Messages</h2>
    
    <div style="margin-bottom: 1rem; display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
      <span style="color: var(--muted);">
        Total: {logStore.logMessages.length} | Filtered: {getFilteredAndSortedLogs().length}
      </span>
    </div>

    <div class="table-container" bind:this={tableContainer}>
      <table class="table" style="min-width: 1000px;">
        <thead>
          <tr>
            <th class="timestamp-col">
              <button class="table-header-btn" onclick={() => setSort('timestamp')}>
                Timestamp {getSortIndicator('timestamp')}
              </button>
              <input type="text" placeholder="Filter" bind:value={logStore.filterTimestamp} />
            </th>
            <th class="level-col">
              <button class="table-header-btn" onclick={() => setSort('level')}>
                Level {getSortIndicator('level')}
              </button>
              <input type="text" placeholder="Filter" bind:value={logStore.filterLevel} />
            </th>
            <th class="source-col">
              <button class="table-header-btn" onclick={() => setSort('source')}>
                Source {getSortIndicator('source')}
              </button>
              <input type="text" placeholder="Filter" bind:value={logStore.filterSource} />
            </th>
            <th class="message-col">
              <button class="table-header-btn" onclick={() => setSort('message')}>
                Message {getSortIndicator('message')}
              </button>
              <input type="text" placeholder="Filter" bind:value={logStore.filterMessage} />
            </th>
          </tr>
        </thead>
        <tbody>
          {#if getFilteredAndSortedLogs().length === 0}
            <tr>
              <td colspan="4" style="text-align: center; color: var(--muted);">
                No log messages received.
              </td>
            </tr>
          {/if}
          {#each getFilteredAndSortedLogs() as log (log.timestamp + log.message)}
            <tr>
              <td>{formatTimestamp(log.timestamp)}</td>
              <td>{log.level}</td>
              <td>{log.source}</td>
              <td>{log.message}</td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  </section>
</div>

<style>
  .page-container {
    padding: 1rem;
  }

  h1 {
    color: var(--accent-2);
    margin-bottom: 1rem;
  }

  h2 {
    color: var(--accent);
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
  }

  .card {
    background: var(--surface);
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border);
  }

  .connection-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group label {
    font-weight: 600;
    color: var(--text);
  }

  .text-input {
    padding: 0.75rem;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    background: var(--background);
    color: var(--text);
    font-size: 1rem;
  }

  .text-input:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 2px rgba(var(--accent-rgb), 0.2);
  }

  .text-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .button-group {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.375rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .btn-primary {
    background: var(--accent);
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background: var(--accent-2);
  }

  .btn-secondary {
    background: var(--muted);
    color: var(--text);
  }

  .btn-secondary:hover:not(:disabled) {
    background: var(--border);
  }

  .btn-danger {
    background: var(--negative);
    color: white;
  }

  .btn-danger:hover:not(:disabled) {
    background: #dc2626;
  }

  .status-grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--background);
    border-radius: 0.375rem;
    border: 1px solid var(--border);
  }

  .status-item.error {
    border-color: var(--negative);
    background: rgba(239, 68, 68, 0.05);
  }

  .status-label {
    font-weight: 600;
    color: var(--text);
  }

  .status-value {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .status-text {
    font-weight: 500;
  }

  .client-id-display {
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--accent);
  }

  .timestamp {
    font-size: 0.875rem;
    color: var(--muted-text);
  }

  .error-message {
    color: var(--negative);
    font-size: 0.875rem;
    font-weight: 500;
  }

  /* Log Messages Table Styles */
  .table-container {
    height: calc(100vh - 650px);
    min-height: 180px;
    overflow-y: auto;
    overflow-x: auto;
    border: 1px solid var(--border);
    border-radius: 4px;
    scroll-behavior: smooth;
  }
  
  .timestamp-col {
    width: 200px;
    min-width: 200px;
  }
  
  .level-col {
    width: 120px;
    min-width: 120px;
  }
  
  .source-col {
    width: 450px;
    min-width: 450px;
  }
  
  .message-col {
    width: auto;
    min-width: 300px;
  }
  
  .table td {
    word-break: break-word;
    vertical-align: top;
  }
  
  .table td:nth-child(4) {
    text-align: left;
  }
  
  .table-header-btn {
    background: none;
    border: none;
    color: inherit;
    font-weight: inherit;
    font-size: inherit;
    cursor: pointer;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
  
  .table-header-btn:hover {
    text-decoration: underline;
  }
  
  .table th input {
    width: calc(100% - 10px);
    padding: 4px;
    margin-top: 5px;
    border: 1px solid var(--border);
    background: var(--surface-2);
    color: var(--text);
    border-radius: 3px;
  }

  @media (max-width: 768px) {
    .button-group {
      flex-direction: column;
    }

    .status-grid {
      grid-template-columns: 1fr;
    }

    .status-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }
</style>