<script lang="ts">
  import { positionsStore, loadPositions } from '$lib/stores/positions.svelte';
  import { clientStore } from '$lib/stores/client.svelte';
  import { formatPrice, formatNumber, formatPnl, formatTimestamp } from '$lib/formatters';

  // Derived state from the global store
  const hasPositions = $derived(positionsStore.positions.length > 0);
  const totalUnrealizedPnlBase = $derived(
    positionsStore.positions.reduce((sum, p) => sum + p.unrealized_pnl_base, 0)
  );
  const totalRealizedPnlBase = $derived(
    positionsStore.positions.reduce((sum, p) => sum + p.realized_pnl_base, 0)
  );
  
  // Check if buttons should be enabled
  const canFetchData = $derived(clientStore.clientId && !positionsStore.isLoading);
</script>

<svelte:head>
  <title>Simulated Trading</title>
</svelte:head>

<section class="card" style="margin-bottom: 1rem;">
  <div style="display:flex; gap:0.75rem; align-items:center; flex-wrap: wrap;">
    <button class="btn" onclick={() => loadPositions()} disabled={!canFetchData}>
      Fetch Positions
    </button>
    <button class="btn" onclick={() => loadPositions(true)} disabled={!canFetchData}>
      Refresh Market Data
    </button>

    {#if positionsStore.isLoading}
      <span style="color: var(--accent-2);">Loading...</span>
    {/if}

    {#if positionsStore.error}
      <span class="pnl-negative">Error: {positionsStore.error}</span>
    {/if}

    {#if hasPositions}
      <span
        class:pnl-positive={totalUnrealizedPnlBase >= 0}
        class:pnl-negative={totalUnrealizedPnlBase < 0}
        class="pnl-total"
      >
        Unrealized P&L: {formatPnl(totalUnrealizedPnlBase)}
      </span>
      <span
        class:pnl-positive={totalRealizedPnlBase >= 0}
        class:pnl-negative={totalRealizedPnlBase < 0}
        class="pnl-total"
      >
        Realized P&L: {formatPnl(totalRealizedPnlBase)}
      </span>
    {/if}
  </div>
</section>

<section class="card">
  <div style="overflow-x: auto;">
    <table class="table" style="min-width: 1200px;">
      <thead>
        <tr>
          <th>Created At</th>
          <th>Updated At</th>
          <th>Strategy</th>
          <th>Symbol</th>
          <th>Size</th>
          <th>Avg. Price</th>
          <th>Current Price</th>
          <th>Unrealized P&L</th>
          <th>Unrealized P&L (Base)</th>
          <th>Realized P&L</th>
          <th>Realized P&L (Base)</th>
        </tr>
      </thead>
      <tbody>
        {#if !hasPositions && !positionsStore.isLoading}
          <tr>
            <td colspan="11" style="text-align: center; color: var(--muted);">
              No positions found.
            </td>
          </tr>
        {/if}
        {#each positionsStore.positions as p (p.position_id)}
          <tr>
            <td>{formatTimestamp(p.timestamp_first_fill)}</td>
            <td>{formatTimestamp(p.timestamp_current)}</td>
            <td>{p.strategy_id}</td>
            <td>{p.symbol_id}</td>
            <td>{formatNumber(p.size)}</td>
            <td>{formatPrice(p.average_price)}</td>
            <td style="background-color: rgba(34, 197, 94, 0.1);">{formatPrice(p.current_price)}</td>
            <td class:pnl-positive={p.unrealized_pnl_local >= 0} class:pnl-negative={p.unrealized_pnl_local < 0}>
              {formatPnl(p.unrealized_pnl_local)}
            </td>
            <td class:pnl-positive={p.unrealized_pnl_base >= 0} class:pnl-negative={p.unrealized_pnl_base < 0}>
              {formatPnl(p.unrealized_pnl_base)}
            </td>
            <td class:pnl-positive={p.realized_pnl_local >= 0} class:pnl-negative={p.realized_pnl_local < 0}>
              {formatPnl(p.realized_pnl_local)}
            </td>
            <td class:pnl-positive={p.realized_pnl_base >= 0} class:pnl-negative={p.realized_pnl_base < 0}>
              {formatPnl(p.realized_pnl_base)}
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>