<!-- Clean LogsPage component without scroll preservation -->
<script lang="ts">
  import { tick } from 'svelte';
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { getFilteredAndSortedLogs, logStore, setSort, reconnectWebSocket } from '$lib/stores/logs.svelte';
  import { formatTimestamp } from '$lib/formatters';
  import type { LogMessage } from '$lib/types';

  let tableContainer: HTMLDivElement | undefined;
  let lastMessageCount = 0;

  function getSortIndicator(column: keyof LogMessage) {
    if (logStore.sortColumn === column) {
      return logStore.sortDirection === 'asc' ? ' ▲' : ' ▼';
    }
    return '';
  }

  function handleReconnect() {
    reconnectWebSocket();
  }

  onMount(() => {
    lastMessageCount = logStore.logMessages.length;
  });

  // Always scroll to bottom when returning to logs page
  $effect(() => {
    const isVisible = $page.url.pathname.startsWith('/logs');
    
    if (isVisible && tableContainer) {
      tick().then(() => {
        if (tableContainer) {
          tableContainer.scrollTo(0, tableContainer.scrollHeight);
        }
      });
    }
  });

  // Auto-scroll to bottom ONLY when new messages arrive AND user was at bottom
  $effect.pre(() => {
    if (!tableContainer) return;

    const currentMessageCount = logStore.logMessages.length;
    
    // Only process if new messages were added
    if (currentMessageCount > lastMessageCount) {
      // Only auto-scroll if user was at the bottom (within 50px)
      const isAtBottom = tableContainer.offsetHeight + tableContainer.scrollTop >= tableContainer.scrollHeight - 50;
      if (isAtBottom) {
        tick().then(() => {
          if (tableContainer) {
            tableContainer.scrollTo(0, tableContainer.scrollHeight);
          }
        });
      }
      lastMessageCount = currentMessageCount;
    }
  });
</script>

<svelte:head>
  <title>Log Messages</title>
</svelte:head>

<div class="page-container">
  <h1>Log Messages</h1>

  <div style="margin-bottom: 1rem; display: flex; align-items: center; gap: 1rem;">
    {#if logStore.wsConnected}
      <span style="color: var(--positive);">WebSocket Connected</span>
    {:else}
      <span style="color: var(--negative);">WebSocket Disconnected</span>
      {#if logStore.wsError}
        <span style="color: var(--negative);"> ({logStore.wsError})</span>
      {/if}
      <button class="btn btn-primary" onclick={handleReconnect}>
        Reconnect
      </button>
    {/if}
    <span style="color: var(--muted);">
      Total messages: {logStore.logMessages.length} | Filtered: {getFilteredAndSortedLogs().length}
    </span>
  </div>

  <section class="card">
    <div class="table-container" bind:this={tableContainer}>
      <table class="table" style="min-width: 1000px;">
        <thead>
          <tr>
            <th class="timestamp-col">
              <button class="table-header-btn" onclick={() => setSort('timestamp')}>
                Timestamp {getSortIndicator('timestamp')}
              </button>
              <input type="text" placeholder="Filter" bind:value={logStore.filterTimestamp} />
            </th>
            <th class="level-col">
              <button class="table-header-btn" onclick={() => setSort('level')}>
                Level {getSortIndicator('level')}
              </button>
              <input type="text" placeholder="Filter" bind:value={logStore.filterLevel} />
            </th>
            <th class="source-col">
              <button class="table-header-btn" onclick={() => setSort('source')}>
                Source {getSortIndicator('source')}
              </button>
              <input type="text" placeholder="Filter" bind:value={logStore.filterSource} />
            </th>
            <th class="message-col">
              <button class="table-header-btn" onclick={() => setSort('message')}>
                Message {getSortIndicator('message')}
              </button>
              <input type="text" placeholder="Filter" bind:value={logStore.filterMessage} />
            </th>
          </tr>
        </thead>
        <tbody>
          {#if getFilteredAndSortedLogs().length === 0}
            <tr>
              <td colspan="4" style="text-align: center; color: var(--muted);">
                No log messages received.
              </td>
            </tr>
          {/if}
          {#each getFilteredAndSortedLogs() as log (log.timestamp + log.message)}
            <tr>
              <td>{formatTimestamp(log.timestamp)}</td>
              <td>{log.level}</td>
              <td>{log.source}</td>
              <td>{log.message}</td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  </section>
</div>

<style>
  .page-container {
    padding: 1rem;
  }
  h1 {
    color: var(--accent-2);
    margin-bottom: 1rem;
  }
  
  .table-container {
    max-height: 82.5vh;
    overflow-y: auto;
    overflow-x: auto;
    border: 1px solid var(--border);
    border-radius: 4px;
    scroll-behavior: smooth;
  }
  
  .timestamp-col {
    width: 200px;
    min-width: 200px;
  }
  
  .level-col {
    width: 120px;
    min-width: 120px;
  }
  
  .source-col {
    width: 450px;
    min-width: 450px;
  }
  
  .message-col {
    width: auto;
    min-width: 300px;
  }
  
  .table td {
    word-break: break-word;
    vertical-align: top;
  }
  
  .table td:nth-child(4) {
    text-align: left; /* Message column left-aligned */
  }
  
  .table-header-btn {
    background: none;
    border: none;
    color: inherit;
    font-weight: inherit;
    font-size: inherit;
    cursor: pointer;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
  .table-header-btn:hover {
    text-decoration: underline;
  }
  .table th input {
    width: calc(100% - 10px); /* Adjust as needed */
    padding: 4px;
    margin-top: 5px;
    border: 1px solid var(--border);
    background: var(--surface-2);
    color: var(--text);
    border-radius: 3px;
  }
</style>