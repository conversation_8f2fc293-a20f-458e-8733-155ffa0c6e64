import { PUBLIC_API_BASE_URL } from '$env/static/public';
import type { PositionsApiResponse, MarketDataRefreshResponse } from './types';
import { clientStore } from './stores/client.svelte';

const API_BASE = PUBLIC_API_BASE_URL || 'http://localhost:8000';

// Client management functions
export async function initializeClient(clientId: string) {
  const response = await fetch(`${API_BASE}/clients/${clientId}/init`, {
    method: 'POST',
  });
  
  if (!response.ok) {
    throw new Error(`Failed to initialize client: ${response.statusText}`);
  }
  
  return response.json();
}

export async function recreateClient(clientId: string) {
  const response = await fetch(`${API_BASE}/clients/${clientId}/recreate`, {
    method: 'POST',
  });
  
  if (!response.ok) {
    throw new Error(`Failed to recreate client: ${response.statusText}`);
  }
  
  return response.json();
}

export async function removeClient(clientId: string) {
  const response = await fetch(`${API_BASE}/clients/${clientId}/remove`, {
    method: 'DELETE',
  });
  
  if (!response.ok) {
    throw new Error(`Failed to remove client: ${response.statusText}`);
  }
  
  return response.json();
}

// Update existing functions to use client_id
export async function fetchPositions(): Promise<PositionsApiResponse> {
  if (!clientStore.clientId) {
    throw new Error('No client ID configured');
  }
  
  const response = await fetch(`${API_BASE}/clients/${clientStore.clientId}/positions`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch positions: ${response.statusText}`);
  }
  
  return response.json();
}

export async function refreshMarketData(): Promise<MarketDataRefreshResponse> {
  if (!clientStore.clientId) {
    throw new Error('No client ID configured');
  }
  
  const response = await fetch(`${API_BASE}/clients/${clientStore.clientId}/market-data/refresh`);
  
  if (!response.ok) {
    throw new Error(`Failed to refresh market data: ${response.statusText}`);
  }
  
  return response.json();
}
