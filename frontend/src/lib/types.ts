export interface Position {
  position_id: string;
  strategy_id: string;
  symbol_id: string;
  timestamp_current: string;
  timestamp_first_fill: string;
  size: number;
  average_price: number;
  current_price: number;
  unrealized_pnl_local: number;
  unrealized_pnl_base: number;
  realized_pnl_local: number;
  realized_pnl_base: number;
}

export interface PositionsApiResponse {
  success: boolean;
  message: string;
  positions: Position[];
}

export interface MarketDataRefreshResponse extends PositionsApiResponse {}

export interface LogMessage {
  timestamp: string; // ISO format, e.g., "2025-08-15T18:48:14.123"
  level: string;     // e.g., "INFO", "WARNING", "ERROR"
  source: string;    // Corresponds to the Python logger's 'name'
  message: string;   // The actual log message content
}
